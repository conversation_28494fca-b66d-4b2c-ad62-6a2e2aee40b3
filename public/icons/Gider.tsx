const GiderIcon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
<svg xmlns="http://www.w3.org/2000/svg" width="51" height="51" viewBox="0 0 51 51" fill="none" stroke="currentColor" {...props}>
<path d="M24.4326 12.7647L28.8699 27.4062L30.3551 25.7409L32.4626 26.5026L33.8432 24.8329L35.6954 25.4581L36.844 23.9927L38.6151 24.4527L33.9216 8.96638C33.8103 8.59913 33.6278 8.25742 33.3845 7.96071C33.1411 7.66401 32.8417 7.41813 32.5033 7.23716C32.165 7.05618 31.7943 6.94366 31.4124 6.90597C31.0305 6.86828 30.6449 6.90619 30.2777 7.01753L24.7471 8.6937L23.3288 9.12342L21.4526 9.69199"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M21.2484 9.75542L21.4656 9.6896C21.7104 9.61534 21.9674 9.59003 22.222 9.61512C22.4766 9.64021 22.7237 9.71521 22.9493 9.83583C23.1749 9.95645 23.3746 10.1203 23.5368 10.3181C23.699 10.5159 23.8207 10.7437 23.8949 10.9885L26.6976 20.2371L22.945 21.3742L20.0839 11.9325C19.9496 11.4894 19.9969 11.0111 20.2152 10.6028C20.4336 10.1945 20.8053 9.8897 21.2484 9.75542Z" fill="#EFF6FF"/>
<path d="M21.4421 9.69689L21.6593 9.63108C21.8788 9.5645 22.1092 9.54181 22.3374 9.56429C22.5656 9.58678 22.7871 9.654 22.9894 9.76212C23.1916 9.87025 23.3705 10.0171 23.516 10.1944C23.6614 10.3717 23.7705 10.5759 23.837 10.7954L26.6982 20.2371L22.9456 21.3742L20.143 12.1256C19.9932 11.6313 20.0459 11.0977 20.2896 10.6422C20.5332 10.1867 20.9478 9.8467 21.4421 9.69689Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M29.0999 16.6821C29.6467 16.6821 30.0899 16.2389 30.0899 15.6921C30.0899 15.1454 29.6467 14.7021 29.0999 14.7021C28.5532 14.7021 28.1099 15.1454 28.1099 15.6921C28.1099 16.2389 28.5532 16.6821 29.0999 16.6821Z" fill="white"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M32.4655 18.6621C33.0122 18.6621 33.4554 18.2189 33.4554 17.6721C33.4554 17.1254 33.0122 16.6821 32.4655 16.6821C31.9187 16.6821 31.4755 17.1254 31.4755 17.6721C31.4755 18.2189 31.9187 18.6621 32.4655 18.6621Z" fill="white"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M32.4555 13.804L29.4067 19.5038L32.4555 13.804Z" fill="white"/>
<path d="M32.4555 13.804L29.4067 19.5038"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M25.8622 13.0374L26.8611 16.2752"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M5.65414 33.9128C5.50515 33.6867 5.40216 33.4334 5.35106 33.1675C5.29997 32.9015 5.30175 32.6281 5.35633 32.3629C5.41092 32.0976 5.51723 31.8457 5.66918 31.6215C5.82112 31.3974 6.01572 31.2053 6.24189 31.0564L10.0957 28.8228L17.2646 38.4255C17.4328 38.6506 17.5531 38.9078 17.6183 39.1812C17.6834 39.4546 17.692 39.7384 17.6435 40.0152C17.5949 40.292 17.4904 40.556 17.3361 40.7909C17.1819 41.0258 16.9813 41.2267 16.7466 41.3813L14.0226 42.8707C13.5659 43.1715 13.0084 43.2786 12.4727 43.1684C11.9371 43.0581 11.4671 42.7396 11.1663 42.283L8.42248 38.1171" fill="#EFF6FF"/>
<path d="M5.65397 33.7776C5.50501 33.5515 5.40208 33.2982 5.35101 33.0323C5.29995 32.7663 5.30178 32.493 5.35638 32.2277C5.41098 31.9625 5.51727 31.7106 5.66922 31.4865C5.82117 31.2623 6.01577 31.0703 6.24194 30.9214L10.0955 28.6877L17.2655 38.2904C17.4336 38.5156 17.5539 38.7728 17.6191 39.0462C17.6842 39.3195 17.6928 39.6034 17.6443 39.8802C17.5958 40.157 17.4912 40.421 17.337 40.6559C17.1827 40.8908 16.9821 41.0917 16.7474 41.2463L14.0238 42.7357C13.7976 42.8847 13.5443 42.9876 13.2784 43.0387C13.0124 43.0898 12.739 43.088 12.4738 43.0334C12.2085 42.9788 11.9566 42.8725 11.7325 42.7205C11.5083 42.5686 11.3163 42.3739 11.1673 42.1478L7.68847 36.8647"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M7.74265 36.9486L5.64685 33.772"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M12.9571 41.2377C13.6678 41.2377 14.2439 40.6616 14.2439 39.9509C14.2439 39.2402 13.6678 38.6641 12.9571 38.6641C12.2464 38.6641 11.6702 39.2402 11.6702 39.9509C11.6702 40.6616 12.2464 41.2377 12.9571 41.2377Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M14.7795 32.4143L17.1431 35.6957"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M14.0342 31.3577L14.8147 32.4652"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M18.6748 25.8019L19.8145 25.7703C21.384 25.8019 22.0917 25.9566 22.7087 26.181L26.0995 27.414C27.3208 27.8582 28.6047 28.1065 29.9036 28.1497L32.9137 28.2501C33.6064 28.2731 34.2671 28.5468 34.7732 29.0203C35.2792 29.4938 35.5961 30.135 35.6651 30.8245L33.068 31.6284C31.3246 32.168 29.5018 32.4059 27.6783 32.3318L22.7735 32.1324"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M14.0342 28.6969L14.5999 28.0252C15.4222 27.0487 16.5185 26.3412 17.747 25.994C18.0332 25.9131 18.3751 25.8133 18.6958 25.8003"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M35.2701 29.5823L43.5526 25.5899C43.831 25.4579 44.1487 25.4348 44.4432 25.5254C44.7378 25.6159 44.9877 25.8134 45.1438 26.079C45.2957 26.3372 45.3484 26.6418 45.292 26.936C45.2357 27.2302 45.0742 27.4938 44.8377 27.6776L39.7727 31.6145C38.949 32.2548 38.0395 32.7763 37.0709 33.1637L32.74 34.8961C31.4229 35.4229 30.0174 35.6936 28.5989 35.6936H21.8854C20.8422 35.6933 19.8238 36.0111 18.9659 36.6046L17.045 37.9346L11.5827 30.6271L14.0081 28.7201"  stroke-miterlimit="10" stroke-linecap="round"/>
</svg>
    );
};

export default GiderIcon;
