const MedikalIcon = ({...props}: React.SVGProps<SVGSVGElement>) => {
    return (
<svg xmlns="http://www.w3.org/2000/svg" width="51" height="51" viewBox="0 0 51 51" fill="none" stroke="currentColor" {...props}>
<path d="M16.7074 35.2524H32.6081V24.0723H16.7074C16.0485 24.0723 15.4166 24.334 14.9506 24.8C14.4847 25.2659 14.223 25.8978 14.223 26.5567V32.768C14.223 33.4269 14.4847 34.0588 14.9506 34.5247C15.4166 34.9907 16.0485 35.2524 16.7074 35.2524Z" fill="#EFF6FF"/>
<path d="M13.1049 5.9668H8.88131C8.62227 5.98076 8.37754 6.08995 8.19411 6.27338C8.01067 6.45682 7.90148 6.70155 7.88752 6.96059V9.94197C7.88752 10.2055 7.99224 10.4583 8.17861 10.6447C8.36498 10.831 8.61774 10.9358 8.88131 10.9358H29.254C29.5176 10.9358 29.7704 10.831 29.9567 10.6447C30.1431 10.4583 30.2478 10.2055 30.2478 9.94197V6.96059C30.2427 6.69861 30.1364 6.44878 29.9511 6.2635C29.7658 6.07822 29.516 5.97189 29.254 5.9668H16.0863" fill="#EFF6FF"/>
<path d="M5.2793 19.2284V18.483C5.2793 15.7113 7.71781 13.5141 10.4967 13.5141L27.3911 13.2656C30.1698 13.2656 32.6085 15.4629 32.6085 18.2346V40.8434C32.6085 42.1612 32.085 43.4251 31.1532 44.357C30.2213 45.2888 28.9574 45.8123 27.6396 45.8123H23.416"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M27.8884 15.2532C29.6117 15.7528 30.6214 17.3441 30.6214 19.2283V22.2097"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M8.75739 11.0286H28.8816V13.513H8.75739V11.0286Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M24.9066 6.06055H29.3786C29.8837 6.06055 30.124 6.30104 30.124 6.80589V9.78727C30.124 10.3438 29.6842 11.0295 29.1302 11.0295H8.75746C8.20367 11.0295 7.76367 10.3438 7.76367 9.78727V6.80589C7.76367 6.31744 8.26901 6.06055 8.75746 6.06055H24.9066Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M16.459 35.1289H32.6081V23.9487H16.459C15.8002 23.9492 15.1685 24.2111 14.7027 24.6769C14.2368 25.1427 13.9749 25.7744 13.9745 26.4332V32.6444C13.9947 33.2969 14.263 33.9172 14.7246 34.3788C15.1862 34.8404 15.8065 35.1087 16.459 35.1289Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M30.3718 27.1777H17.9495"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M30.1247 32.147H23.4166"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M23.4152 29.6626H20.4338"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M30.8699 29.6626H25.6525"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M32.6074 19.2289V18.4835C32.6074 15.7118 30.1688 13.5146 27.39 13.5146L10.4955 13.2661C7.71688 13.2661 5.27812 15.4634 5.27812 18.2351V40.8439C5.27812 42.1617 5.80165 43.4256 6.73351 44.3574C7.66537 45.2893 8.92923 45.8128 10.2471 45.8128H23.5665"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M9.99976 15.2537C8.27652 15.7533 7.26683 17.3446 7.26683 19.2288V22.2102"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M29.1293 11.0291H9.00502V13.5135H29.1293V11.0291Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M41.0562 17.7375H42.05C42.9066 17.7375 43.7281 18.0778 44.3338 18.6835C44.9395 19.2893 45.2798 20.1108 45.2798 20.9674V40.3463C45.2773 41.2022 44.9362 42.0222 44.331 42.6274C43.7258 43.2326 42.9058 43.5737 42.05 43.5762H31.8701"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M32.7795 17.7368H41.1482"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M41.1487 23.4513C42.0406 23.4513 42.7636 22.7282 42.7636 21.8363C42.7636 20.9445 42.0406 20.2214 41.1487 20.2214C40.2568 20.2214 39.5338 20.9445 39.5338 21.8363C39.5338 22.7282 40.2568 23.4513 41.1487 23.4513Z" fill="#EFF6FF"/>
<path d="M40.9316 29.1654C41.8234 29.1654 42.5465 28.4424 42.5465 27.5505C42.5465 26.6586 41.8234 25.9355 40.9316 25.9355C40.0397 25.9355 39.3167 26.6586 39.3167 27.5505C39.3167 28.4424 40.0397 29.1654 40.9316 29.1654Z" fill="#EFF6FF"/>
<path d="M40.9316 34.8795C41.8234 34.8795 42.5465 34.1565 42.5465 33.2646C42.5465 32.3727 41.8234 31.6497 40.9316 31.6497C40.0397 31.6497 39.3167 32.3727 39.3167 33.2646C39.3167 34.1565 40.0397 34.8795 40.9316 34.8795Z" fill="#EFF6FF"/>
<path d="M41.1112 40.2213C42.0031 40.2213 42.7261 39.4983 42.7261 38.6064C42.7261 37.7145 42.0031 36.9915 41.1112 36.9915C40.2193 36.9915 39.4963 37.7145 39.4963 38.6064C39.4963 39.4983 40.2193 40.2213 41.1112 40.2213Z" fill="#EFF6FF"/>
<path d="M36.5836 19.9727V41.0908"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M40.9313 23.4513C41.8231 23.4513 42.5462 22.7282 42.5462 21.8363C42.5462 20.9445 41.8231 20.2214 40.9313 20.2214C40.0394 20.2214 39.3163 20.9445 39.3163 21.8363C39.3163 22.7282 40.0394 23.4513 40.9313 23.4513Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M40.9313 29.1654C41.8231 29.1654 42.5462 28.4424 42.5462 27.5505C42.5462 26.6586 41.8231 25.9355 40.9313 25.9355C40.0394 25.9355 39.3163 26.6586 39.3163 27.5505C39.3163 28.4424 40.0394 29.1654 40.9313 29.1654Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M40.9313 34.8795C41.8231 34.8795 42.5462 34.1565 42.5462 33.2646C42.5462 32.3727 41.8231 31.6497 40.9313 31.6497C40.0394 31.6497 39.3163 32.3727 39.3163 33.2646C39.3163 34.1565 40.0394 34.8795 40.9313 34.8795Z"  stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M40.9313 40.3458C41.8231 40.3458 42.5462 39.6228 42.5462 38.7309C42.5462 37.839 41.8231 37.116 40.9313 37.116C40.0394 37.116 39.3163 37.839 39.3163 38.7309C39.3163 39.6228 40.0394 40.3458 40.9313 40.3458Z"  stroke-miterlimit="10" stroke-linecap="round"/>
</svg>
    );
};

export default MedikalIcon;
